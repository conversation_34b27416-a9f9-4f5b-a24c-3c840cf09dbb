# Redis Exporter Configuration
# This file can be used to configure a Redis exporter for Prometheus monitoring
# To enable Redis monitoring, uncomment the redis-exporter service in docker-compose.yml

# Example configuration for Redis exporter
redis:
  addr: "redis://redis:6379"
  password: ""
  db: 0

# Metrics configuration
metrics:
  enabled: true
  include_system_metrics: true
  include_config_metrics: true
  include_commandstats_metrics: true
  include_keyspace_metrics: true
