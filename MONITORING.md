# Monitoring Setup with Prometheus and Grafana

This document describes the monitoring setup for the Salon AI Voice Agent using Prometheus and Grafana.

## Services Overview

The Docker Compose file includes the following monitoring services:

### External Services (Cloud-hosted)
- **PostgreSQL**: Database storage (cloud-hosted)
- **Redis**: Caching and session storage (cloud-hosted)

### Local Monitoring Services

#### Prometheus
- **Container**: `salon_prometheus`
- **Port**: `9090`
- **Purpose**: Metrics collection and storage
- **Configuration**: `prometheus.yml`

#### Grafana
- **Container**: `salon_grafana`
- **Port**: `3000`
- **Purpose**: Metrics visualization and dashboards
- **Default Credentials**:
  - Username: `admin`
  - Password: `admin`

## Prerequisites

- Ensure your cloud-hosted PostgreSQL and Redis services are configured and accessible
- Your LiveKit agent should be configured to connect to the cloud services
- Make sure your application exposes metrics on port 8000

## Getting Started

1. **Start monitoring services**:
   ```bash
   docker-compose up -d
   ```

2. **Access Prometheus**:
   - URL: http://localhost:9090
   - Check targets status at: http://localhost:9090/targets

3. **Access Grafana**:
   - URL: http://localhost:3000
   - Login with admin/admin
   - Change password on first login



## Pre-configured Components

### Data Source
- Prometheus is automatically configured as the default data source
- Connection URL: `http://prometheus:9090`

### Dashboard
- **LiveKit Agent Monitoring** dashboard is pre-installed
- Monitors STT performance and LLM token usage
- Auto-refreshes every 5 seconds

## Metrics Configuration

The Prometheus configuration (`prometheus.yml`) is set to scrape metrics from:
- **Target**: `host.docker.internal:8000`
- **Job**: `livekit-agent`
- **Scrape Interval**: 10 seconds

Make sure your LiveKit agent is exposing metrics on port 8000.

## Customization

### Adding New Dashboards
1. Create JSON dashboard files in `grafana/provisioning/dashboards/`
2. Restart Grafana container: `docker-compose restart grafana`

### Adding New Data Sources
1. Edit `grafana/provisioning/datasources/prometheus.yml`
2. Restart Grafana container

### Modifying Prometheus Targets
1. Edit `prometheus.yml`
2. Restart Prometheus container: `docker-compose restart prometheus`

## Troubleshooting

### Prometheus Can't Reach Targets
- Ensure your application is running and exposing metrics
- Check if port 8000 is accessible from Docker containers
- Verify `host.docker.internal` resolves correctly

### Grafana Dashboard Not Loading
- Check Prometheus data source connection in Grafana
- Verify metrics are being collected in Prometheus
- Check container logs: `docker-compose logs grafana`

### Data Persistence
- Prometheus data: Stored in `prometheus_data` volume
- Grafana data: Stored in `grafana_data` volume
- Data persists across container restarts
- Database and cache data are managed by your cloud providers

## Monitoring Metrics

Based on your LiveKit Agent setup, the following metrics should be available:

- **STT Metrics**: Speech-to-text processing duration and latency
- **LLM Metrics**: Token usage and response times
- **TTS Metrics**: Text-to-speech processing duration
- **EOU Metrics**: End-of-utterance detection timing

## Security Notes

- Change default Grafana admin password in production
- Consider adding authentication to Prometheus in production
- Use environment variables for sensitive configuration
