# Monitoring Setup with Prometheus and Grafana

This document describes the monitoring setup for the Salon AI Voice Agent using Prometheus and Grafana.

## Services Overview

The Docker Compose file includes the following monitoring services:

### External Services (Cloud-hosted)
- **PostgreSQL**: Database storage (cloud-hosted)
- **Redis**: Caching and session storage (cloud-hosted)

### Local Services

#### LiveKit Agent
- **Container**: `salon_livekit_agent`
- **Port**: `8000` (metrics endpoint)
- **Purpose**: Voice agent application
- **Build**: Uses local Dockerfile
- **Volumes**: `./recordings`, `./logs`

#### Prometheus
- **Container**: `salon_prometheus`
- **Port**: `9090`
- **Purpose**: Metrics collection and storage
- **Configuration**: `prometheus.yml`

#### Grafana
- **Container**: `salon_grafana`
- **Port**: `3000`
- **Purpose**: Metrics visualization and dashboards
- **Default Credentials**:
  - Username: `admin`
  - Password: `admin`

## Prerequisites

- Ensure your cloud-hosted PostgreSQL and Redis services are configured and accessible
- Create a `.env` file with all required environment variables (database, Redis, API keys, etc.)
- Docker and Docker Compose installed

## Getting Started

1. **Build and start all services**:
   ```bash
   docker-compose up -d --build
   ```

2. **Check service status**:
   ```bash
   docker-compose ps
   ```

3. **Access the services**:
   - **LiveKit Agent**: Running in container (metrics on port 8000)
   - **Prometheus**: http://localhost:9090
   - **Grafana**: http://localhost:3000 (admin/admin)

4. **Verify metrics collection**:
   - Check Prometheus targets: http://localhost:9090/targets
   - The LiveKit agent target should show as "UP"



## Pre-configured Components

### Data Source
- Prometheus is automatically configured as the default data source
- Connection URL: `http://prometheus:9090`

### Dashboard
- **LiveKit Agent Monitoring** dashboard is pre-installed
- Monitors STT performance and LLM token usage
- Auto-refreshes every 5 seconds

## Metrics Configuration

The Prometheus configuration (`prometheus.yml`) is set to scrape metrics from:
- **LiveKit Agent**: `livekit-agent:8000` (container-to-container communication)
- **Prometheus**: `localhost:9090` (self-monitoring)
- **Scrape Interval**: 10 seconds for agent, 15 seconds for Prometheus

The LiveKit agent automatically exposes metrics on port 8000 when running.

## Customization

### Adding New Dashboards
1. Create JSON dashboard files in `grafana/provisioning/dashboards/`
2. Restart Grafana container: `docker-compose restart grafana`

### Adding New Data Sources
1. Edit `grafana/provisioning/datasources/prometheus.yml`
2. Restart Grafana container

### Modifying Prometheus Targets
1. Edit `prometheus.yml`
2. Restart Prometheus container: `docker-compose restart prometheus`

## Troubleshooting

### LiveKit Agent Issues
- Check container logs: `docker-compose logs livekit-agent`
- Verify `.env` file contains all required variables
- Ensure cloud database and Redis are accessible from container

### Prometheus Can't Reach Targets
- Check if LiveKit agent container is running: `docker-compose ps`
- Verify agent is exposing metrics: `docker-compose logs livekit-agent`
- Check network connectivity between containers

### Grafana Dashboard Not Loading
- Check Prometheus data source connection in Grafana
- Verify metrics are being collected in Prometheus
- Check container logs: `docker-compose logs grafana`

### Data Persistence
- Prometheus data: Stored in `prometheus_data` volume
- Grafana data: Stored in `grafana_data` volume
- Data persists across container restarts
- Database and cache data are managed by your cloud providers

## Monitoring Metrics

Based on your LiveKit Agent setup, the following metrics should be available:

- **STT Metrics**: Speech-to-text processing duration and latency
- **LLM Metrics**: Token usage and response times
- **TTS Metrics**: Text-to-speech processing duration
- **EOU Metrics**: End-of-utterance detection timing

## Security Notes

- Change default Grafana admin password in production
- Consider adding authentication to Prometheus in production
- Use environment variables for sensitive configuration
